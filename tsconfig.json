{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@chatpay/shared": ["./packages/shared/src/index.ts"], "@chatpay/shared/*": ["./packages/shared/src/*"], "@chatpay/api": ["./packages/api/src/index.ts"], "@chatpay/api/*": ["./packages/api/src/*"], "@chatpay/payments": ["./packages/payments/src/index.ts"], "@chatpay/payments/*": ["./packages/payments/src/*"], "@chatpay/crypto": ["./packages/crypto/src/index.ts"], "@chatpay/crypto/*": ["./packages/crypto/src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}