{"name": "chat<PERSON>y", "version": "1.0.0", "description": "Secure WhatsApp alternative with built-in fiat/crypto payments", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "type-check": "turbo run type-check", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "devDependencies": {"@turbo/gen": "^1.10.12", "turbo": "^1.10.12", "typescript": "^5.2.2", "prettier": "^3.0.3", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0"}, "dependencies": {"@prisma/client": "^5.4.2", "prisma": "^5.4.2"}, "packageManager": "npm@9.8.1", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}