// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  username          String   @unique
  displayName       String
  avatar            String?
  phoneNumber       String?
  isVerified        <PERSON>ole<PERSON>  @default(false)
  isOn<PERSON>  @default(false)
  lastSeen          DateTime @default(now())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Firebase Auth
  firebaseUid       String   @unique

  // Signal Protocol Keys
  identityKey       String?
  registrationId    Int?
  preKeys           Json?    // Store pre-keys as JSO<PERSON>
  signedPreKey      <PERSON>son?

  // Relationships
  sentMessages      Message[]       @relation("MessageSender")
  chatMembers       ChatMember[]
  bankAccounts      BankAccount[]
  cryptoWallets     CryptoWallet[]
  sentPayments      Payment[]       @relation("PaymentSender")
  receivedPayments  Payment[]       @relation("PaymentReceiver")
  sentCryptoTxs     CryptoTransaction[] @relation("CryptoSender")
  receivedCryptoTxs CryptoTransaction[] @relation("CryptoReceiver")
  contacts          Contact[]       @relation("UserContacts")
  contactOf         Contact[]       @relation("ContactUser")

  @@map("users")
}

model Chat {
  id          String   @id @default(cuid())
  name        String?  // For group chats
  description String?
  avatar      String?
  isGroup     Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  members     ChatMember[]
  messages    Message[]

  @@map("chats")
}

model ChatMember {
  id       String @id @default(cuid())
  userId   String
  chatId   String
  role     ChatRole @default(MEMBER)
  joinedAt DateTime @default(now())

  // Relationships
  user     User @relation(fields: [userId], references: [id], onDelete: Cascade)
  chat     Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)

  @@unique([userId, chatId])
  @@map("chat_members")
}

model Message {
  id          String      @id @default(cuid())
  content     String?     // Encrypted content
  messageType MessageType @default(TEXT)
  fileUrl     String?
  fileName    String?
  fileSize    Int?
  isEdited    Boolean     @default(false)
  isDeleted   Boolean     @default(false)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relationships
  senderId    String
  chatId      String
  sender      User @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  chat        Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)

  // Message Status
  messageStatus MessageStatus[]

  @@map("messages")
}

model MessageStatus {
  id        String @id @default(cuid())
  messageId String
  userId    String
  status    DeliveryStatus @default(SENT)
  timestamp DateTime @default(now())

  // Relationships
  message   Message @relation(fields: [messageId], references: [id], onDelete: Cascade)

  @@unique([messageId, userId])
  @@map("message_status")
}

model Contact {
  id        String   @id @default(cuid())
  userId    String
  contactId String
  nickname  String?
  createdAt DateTime @default(now())

  // Relationships
  user      User @relation("UserContacts", fields: [userId], references: [id], onDelete: Cascade)
  contact   User @relation("ContactUser", fields: [contactId], references: [id], onDelete: Cascade)

  @@unique([userId, contactId])
  @@map("contacts")
}

model BankAccount {
  id              String   @id @default(cuid())
  userId          String
  plaidAccountId  String   @unique
  plaidItemId     String
  accountName     String
  accountType     String
  accountSubtype  String?
  mask            String
  isVerified      Boolean  @default(false)
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  user            User @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments        Payment[]

  @@map("bank_accounts")
}

model Payment {
  id              String        @id @default(cuid())
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  description     String?
  status          PaymentStatus @default(PENDING)
  paymentMethod   PaymentMethod
  externalId      String?       // Dwolla/Stripe transaction ID
  failureReason   String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relationships
  senderId        String
  receiverId      String
  bankAccountId   String?
  sender          User @relation("PaymentSender", fields: [senderId], references: [id])
  receiver        User @relation("PaymentReceiver", fields: [receiverId], references: [id])
  bankAccount     BankAccount? @relation(fields: [bankAccountId], references: [id])

  @@map("payments")
}

model CryptoWallet {
  id          String   @id @default(cuid())
  userId      String
  address     String   @unique
  network     String   // ethereum, bitcoin, etc.
  walletType  String   // fireblocks, metamask, etc.
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)
  sentTxs     CryptoTransaction[] @relation("CryptoWalletSent")
  receivedTxs CryptoTransaction[] @relation("CryptoWalletReceived")

  @@map("crypto_wallets")
}

model CryptoTransaction {
  id              String              @id @default(cuid())
  amount          String              // Use string for crypto precision
  currency        String              // BTC, ETH, USDC, etc.
  network         String
  txHash          String?             @unique
  status          CryptoTxStatus      @default(PENDING)
  gasPrice        String?
  gasFee          String?
  blockNumber     Int?
  confirmations   Int                 @default(0)
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  // Relationships
  senderId        String
  receiverId      String
  senderWalletId  String
  receiverWalletId String?
  sender          User @relation("CryptoSender", fields: [senderId], references: [id])
  receiver        User @relation("CryptoReceiver", fields: [receiverId], references: [id])
  senderWallet    CryptoWallet @relation("CryptoWalletSent", fields: [senderWalletId], references: [id])
  receiverWallet  CryptoWallet? @relation("CryptoWalletReceived", fields: [receiverWalletId], references: [id])

  @@map("crypto_transactions")
}

// Enums
enum ChatRole {
  ADMIN
  MEMBER
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  AUDIO
  VIDEO
  PAYMENT
  CRYPTO_PAYMENT
}

enum DeliveryStatus {
  SENT
  DELIVERED
  READ
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum PaymentMethod {
  ACH
  WIRE
  CARD
  DWOLLA
}

enum CryptoTxStatus {
  PENDING
  CONFIRMED
  FAILED
  CANCELLED
}
