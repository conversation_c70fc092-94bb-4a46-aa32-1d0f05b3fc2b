# ChatPay - Secure Messaging & Payments Platform

A modern, secure WhatsApp alternative with built-in fiat and cryptocurrency payment functionality. Built with TypeScript, featuring end-to-end encryption, real-time messaging, and seamless payment integration.

## 🌟 Features

### 💬 Secure Messaging
- **End-to-End Encryption**: Signal Protocol implementation for maximum security
- **Real-time Communication**: Socket.IO powered instant messaging
- **Rich Media Support**: Send images, files, audio, and video
- **Group Chats**: Create and manage group conversations
- **Message Status**: Delivery and read receipts

### 💰 Payment Integration
- **Fiat Payments**: Bank integration via Plaid + Dwolla/Stripe
- **Cryptocurrency**: Built-in crypto wallet with Fireblocks integration
- **Instant Transfers**: Send money with just a message
- **Payment History**: Track all transactions securely
- **Multi-currency Support**: USD, EUR, BTC, ETH, and more

### 🔐 Security & Privacy
- **Signal Protocol**: Military-grade encryption for all messages
- **Firebase Authentication**: Secure user management
- **JWT <PERSON>kens**: Stateless authentication with refresh tokens
- **Data Encryption**: All sensitive data encrypted at rest
- **Privacy First**: No data collection or tracking

### 🌍 Cross-Platform
- **Web Application**: Next.js 14 with modern UI
- **Mobile Apps**: React Native with Expo (iOS & Android)
- **Real-time Sync**: Seamless experience across all devices
- **Responsive Design**: Beautiful UI that works everywhere

## 🛠️ Tech Stack

### Frontend
- **Web**: Next.js 14, React 18, TypeScript
- **Mobile**: React Native, Expo
- **UI**: Tailwind CSS, Radix UI, ShadCN/UI
- **State Management**: Zustand
- **Real-time**: Socket.IO Client

### Backend
- **API**: NestJS, TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Firebase Auth + JWT
- **Real-time**: Socket.IO Server
- **Encryption**: Signal Protocol

### Payments & Crypto
- **Bank Integration**: Plaid API
- **Payment Processing**: Dwolla, Stripe
- **Crypto Wallet**: Fireblocks API
- **Security**: End-to-end encrypted transactions

### Infrastructure
- **Monorepo**: TurboRepo for efficient builds
- **Deployment**: Vercel (Web), Expo EAS (Mobile)
- **Database**: Supabase/Neon PostgreSQL
- **Monitoring**: Sentry, Logtail

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Firebase project
- Plaid developer account (for payments)

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/yourusername/chatpay.git
cd chatpay
```

2. **Run the installation script**
```bash
chmod +x install.sh
./install.sh
```

3. **Configure environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Set up the database**
```bash
npm run db:migrate
npm run db:seed  # Optional: Add sample data
```

5. **Start development servers**
```bash
npm run dev
```

This will start:
- Web app: http://localhost:3002
- API server: http://localhost:3000
- Socket.IO server: http://localhost:3001

## 📁 Project Structure

```
chatpay/
├── apps/
│   ├── web/           # Next.js web application
│   └── mobile/        # React Native mobile app
├── packages/
│   ├── api/           # NestJS backend API
│   ├── shared/        # Shared types and utilities
│   ├── payments/      # Payment integration
│   └── crypto/        # Cryptocurrency functionality
├── prisma/            # Database schema and migrations
├── docs/              # Documentation
└── config files       # TurboRepo, TypeScript, etc.
```

## 🎨 Design System

ChatPay uses a custom design system with your preferred color palette:

- **Primary**: Purple (#8B5CF6, #7C3AED)
- **Accent**: Gold (#F59E0B, #D97706)
- **Neutral**: Black (#000000), Gray (#6B7280, #374151)
- **Theme**: Dark mode with purple/gold gradients

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev          # Start all development servers
npm run build        # Build all packages
npm run lint         # Lint all packages
npm run test         # Run tests

# Database
npm run db:generate  # Generate Prisma client
npm run db:migrate   # Run database migrations
npm run db:push      # Push schema changes
npm run db:studio    # Open Prisma Studio

# Individual packages
npm run dev:web      # Start web app only
npm run dev:api      # Start API server only
npm run dev:mobile   # Start mobile app only
```

### Environment Variables

Key environment variables you need to configure:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/chatpay"

# Firebase
FIREBASE_API_KEY="your-firebase-api-key"
FIREBASE_PROJECT_ID="your-project-id"

# JWT
JWT_SECRET="your-super-secret-jwt-key"

# Plaid (for bank integration)
PLAID_CLIENT_ID="your-plaid-client-id"
PLAID_SECRET="your-plaid-secret"

# Dwolla (for payments)
DWOLLA_KEY="your-dwolla-key"
DWOLLA_SECRET="your-dwolla-secret"

# Fireblocks (for crypto)
FIREBLOCKS_API_KEY="your-fireblocks-api-key"
```

## 🔐 Security Features

### Signal Protocol Implementation
- **Identity Keys**: Unique identity for each user
- **Pre-keys**: One-time keys for initial message exchange
- **Session Keys**: Ephemeral keys for ongoing conversations
- **Double Ratchet**: Forward secrecy and break-in recovery

### Payment Security
- **Bank-grade Encryption**: All payment data encrypted
- **PCI Compliance**: Secure payment processing
- **Fraud Detection**: Real-time transaction monitoring
- **Multi-factor Authentication**: Additional security layers

## 📱 Mobile App

The mobile app is built with React Native and Expo:

```bash
cd apps/mobile
npm run start        # Start Expo development server
npm run ios          # Run on iOS simulator
npm run android      # Run on Android emulator
npm run build        # Build for production
```

## 🚀 Deployment

### Web Application (Vercel)
```bash
npm run build:web
# Deploy to Vercel
```

### API Server (Railway/Render)
```bash
npm run build:api
# Deploy to your preferred platform
```

### Mobile Apps (Expo EAS)
```bash
cd apps/mobile
eas build --platform all
eas submit --platform all
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs on GitHub Issues
- **Discussions**: Join our GitHub Discussions
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- **Signal Protocol**: For providing the encryption foundation
- **Firebase**: For authentication and real-time features
- **Plaid**: For secure bank connectivity
- **Vercel**: For hosting and deployment
- **Open Source Community**: For the amazing tools and libraries

---

Built with ❤️ by the ChatPay Team

**Secure. Private. Instant.**
