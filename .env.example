# Database
DATABASE_URL="postgresql://username:password@localhost:5432/chatpay"

# Firebase Configuration
FIREBASE_API_KEY=""
FIREBASE_AUTH_DOMAIN=""
FIREBASE_PROJECT_ID=""
FIREBASE_STORAGE_BUCKET=""
FIREBASE_MESSAGING_SENDER_ID=""
FIREBASE_APP_ID=""
FIREBASE_MEASUREMENT_ID=""

# JWT Secret
JWT_SECRET="your-super-secret-jwt-key-here"

# Plaid Configuration
PLAID_CLIENT_ID=""
PLAID_SECRET=""
PLAID_ENV="sandbox" # sandbox, development, production

# Dwolla Configuration
DWOLLA_KEY=""
DWOLLA_SECRET=""
DWOLLA_ENVIRONMENT="sandbox" # sandbox, production

# Stripe Configuration
STRIPE_SECRET_KEY=""
STRIPE_PUBLISHABLE_KEY=""
STRIPE_WEBHOOK_SECRET=""

# Fireblocks Configuration (Optional)
FIREBLOCKS_API_KEY=""
FIREBLOCKS_PRIVATE_KEY=""
FIREBLOCKS_BASE_URL="https://sandbox-api.fireblocks.io"

# Socket.IO Configuration
SOCKET_IO_PORT=3001

# API Configuration
API_PORT=3000
API_URL="http://localhost:3000"
WEB_URL="http://localhost:3002"

# Encryption Keys
SIGNAL_IDENTITY_KEY=""
SIGNAL_REGISTRATION_ID=""

# Monitoring & Logging
SENTRY_DSN=""
LOGTAIL_TOKEN=""

# File Upload
UPLOAD_MAX_SIZE="10mb"
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,application/pdf,text/plain"
