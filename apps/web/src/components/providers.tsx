'use client';

import { ReactNode } from 'react';
import { AuthProvider } from '@/lib/auth/auth-provider';
import { SocketProvider } from '@/lib/socket/socket-provider';

interface ProvidersProps {
  children: ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <AuthProvider>
      <SocketProvider>
        {children}
      </SocketProvider>
    </AuthProvider>
  );
}
