'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { MessageBubble } from './message-bubble';
import { useAuthStore } from '@/store/auth';
import { 
  Send, 
  Paperclip, 
  Smile, 
  Phone, 
  Video, 
  MoreVertical,
  DollarSign,
  Shield
} from 'lucide-react';

interface ChatWindowProps {
  chatId: string;
}

export function ChatWindow({ chatId }: ChatWindowProps) {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { user } = useAuthStore();

  // Mock chat data - in real app this would come from API/store
  const chatInfo = {
    id: chatId,
    name: '<PERSON>',
    avatar: null,
    isOnline: true,
    lastSeen: 'Last seen 2 minutes ago',
    isGroup: false,
    isEncrypted: true,
  };

  // Mock messages - in real app this would come from API/store
  const messages = [
    {
      id: '1',
      senderId: 'other',
      senderName: 'Alice <PERSON>',
      content: 'Hey! How are you doing?',
      timestamp: new Date(Date.now() - 120000), // 2 minutes ago
      messageType: 'TEXT' as const,
      isDelivered: true,
      isRead: true,
    },
    {
      id: '2',
      senderId: user?.id || 'me',
      senderName: user?.displayName || 'You',
      content: "I'm doing great! Just working on some new features for ChatPay.",
      timestamp: new Date(Date.now() - 60000), // 1 minute ago
      messageType: 'TEXT' as const,
      isDelivered: true,
      isRead: true,
    },
    {
      id: '3',
      senderId: 'other',
      senderName: 'Alice Johnson',
      content: 'That sounds exciting! Can you send me $50 for lunch?',
      timestamp: new Date(Date.now() - 30000), // 30 seconds ago
      messageType: 'TEXT' as const,
      isDelivered: true,
      isRead: false,
    },
    {
      id: '4',
      senderId: user?.id || 'me',
      senderName: user?.displayName || 'You',
      content: '',
      timestamp: new Date(Date.now() - 10000), // 10 seconds ago
      messageType: 'PAYMENT' as const,
      paymentData: {
        amount: 50,
        currency: 'USD',
        status: 'COMPLETED',
      },
      isDelivered: true,
      isRead: false,
    },
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (message.trim()) {
      // In real app, this would send the message via socket/API
      console.log('Sending message:', message);
      setMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <div className="p-4 border-b border-gray-700 bg-gray-900/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Avatar className="w-10 h-10">
                <AvatarImage src={chatInfo.avatar || ''} />
                <AvatarFallback className="bg-primary-600 text-white">
                  {getInitials(chatInfo.name)}
                </AvatarFallback>
              </Avatar>
              {chatInfo.isOnline && (
                <div className="online-indicator" />
              )}
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <h2 className="font-semibold text-white">{chatInfo.name}</h2>
                {chatInfo.isEncrypted && (
                  <Shield className="w-4 h-4 text-green-500" title="End-to-end encrypted" />
                )}
              </div>
              <p className="text-sm text-gray-400">
                {chatInfo.isOnline ? 'Online' : chatInfo.lastSeen}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
              <Phone className="w-5 h-5" />
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
              <Video className="w-5 h-5" />
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
              <DollarSign className="w-5 h-5" />
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
              <MoreVertical className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 custom-scrollbar">
        {messages.map((msg) => (
          <MessageBubble
            key={msg.id}
            message={msg}
            isOwn={msg.senderId === user?.id}
          />
        ))}
        
        {isTyping && (
          <div className="flex items-center space-x-2 text-gray-400">
            <div className="typing-indicator">
              <div className="typing-dot" style={{ '--delay': '0ms' } as any} />
              <div className="typing-dot" style={{ '--delay': '150ms' } as any} />
              <div className="typing-dot" style={{ '--delay': '300ms' } as any} />
            </div>
            <span className="text-sm">{chatInfo.name} is typing...</span>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-gray-700 bg-gray-900/50">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
            <Paperclip className="w-5 h-5" />
          </Button>
          
          <div className="flex-1 relative">
            <Input
              placeholder="Type a message..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 pr-12"
            />
            <Button 
              variant="ghost" 
              size="icon" 
              className="absolute right-1 top-1 text-gray-400 hover:text-white"
            >
              <Smile className="w-4 h-4" />
            </Button>
          </div>

          <Button
            onClick={handleSendMessage}
            disabled={!message.trim()}
            className="chatpay-gradient hover:opacity-90 transition-opacity"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
