'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { MessageBubble } from './message-bubble';
import { useAuthStore } from '@/store/auth';
import { 
  Send, 
  Paperclip, 
  Smile, 
  Phone, 
  Video, 
  MoreVertical,
  DollarSign,
  Shield,
  ChevronLeft
} from 'lucide-react';

interface ChatWindowProps {
  chatId: string;
  onBack?: () => void;
}

type PaymentStatus = 'COMPLETED' | 'PENDING' | 'PROCESSING' | 'FAILED';

interface PaymentData {
  amount: number;
  currency: string;
  status: PaymentStatus;
}

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: Date;
  messageType: 'TEXT' | 'PAYMENT' | 'IMAGE' | 'FILE' | 'AUDIO' | 'VIDEO' | 'CRYPTO_PAYMENT';
  paymentData?: PaymentData;
  isDelivered: boolean;
  isRead: boolean;
}

export function ChatWindow({ chatId, onBack }: ChatWindowProps) {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { user } = useAuthStore();
  
  // Mock chat data - in real app this would come from API/store
  const chatInfo = {
    id: chatId,
    name: 'Alice Johnson',
    avatar: null as string | null,
    isOnline: true,
    lastSeen: 'Last seen 2 minutes ago',
    isGroup: false,
    isEncrypted: true,
  };
  
  // Mock messages - in real app this would come from API/store
  const messages: Message[] = [
    {
      id: '1',
      senderId: 'other',
      senderName: 'Alice Johnson',
      content: 'Hey! How are you doing?',
      messageType: 'TEXT' as const,
      timestamp: new Date(Date.now() - 120000), // 2 minutes ago
      isDelivered: true,
      isRead: true,
    },
    {
      id: '2',
      senderId: 'me',
      senderName: user?.displayName || 'Me',
      content: 'I\'m doing great, thanks for asking! How about you?',
      messageType: 'TEXT' as const,
      timestamp: new Date(Date.now() - 60000), // 1 minute ago
      isDelivered: true,
      isRead: true,
    },
    {
      id: '3',
      senderId: 'other',
      senderName: 'Alice Johnson',
      content: 'Can you send me $50 for lunch?',
      messageType: 'PAYMENT' as const,
      timestamp: new Date(),
      paymentData: {
        amount: 50,
        currency: 'USD',
        status: 'PENDING' as const,
      },
      isDelivered: true,
      isRead: true,
    },
  ];
  
  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  
  const handleSendMessage = () => {
    if (!message.trim()) return;
    
    // In a real app, you would send the message to your backend
    console.log('Sending message:', message);
    
    // For demo purposes, we'll just clear the input
    setMessage('');
    
    // Scroll to bottom after sending a message
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };
  
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };
  
  const renderMessages = (): JSX.Element[] => {
    return messages.map((msg) => (
      <div
        key={msg.id}
        className={`flex ${msg.senderId === 'me' ? 'justify-end' : 'justify-start'}`}
      >
        <div
          className={`max-w-xs md:max-w-md lg:max-w-lg xl:max-w-2xl rounded-2xl px-4 py-3 backdrop-blur-md ${
            msg.senderId === 'me'
              ? 'bg-purple-600/30 text-white rounded-br-none border border-purple-500/30'
              : 'bg-gray-800/50 text-white rounded-bl-none border border-gray-700/50'
          }`}
        >
          {msg.messageType === 'PAYMENT' && msg.paymentData ? (
            <div className="p-3 rounded-lg bg-purple-500/10">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Payment Request</span>
                <DollarSign className="h-4 w-4 text-purple-300" />
              </div>
              <div className="text-2xl font-bold text-purple-100">
                ${msg.paymentData.amount.toFixed(2)} {msg.paymentData.currency}
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-sm text-purple-200/70">
                  {msg.paymentData.status.toLowerCase()}
                </span>
                <Button 
                  size="sm" 
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  {msg.senderId === 'me' ? 'View' : 'Pay'}
                </Button>
              </div>
            </div>
          ) : (
            <p className="text-white/90">{msg.content}</p>
          )}
          <div className="text-xs text-right mt-1 text-white/50">
            {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      </div>
    ));
  };
  
  if (!chatInfo) {
    return <div className="flex-1 flex items-center justify-center text-gray-400">Loading chat...</div>;
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-b from-[#1E1B4B] to-[#0F172A]">
      {/* Chat Header */}
      <div className="p-4 border-b border-purple-900/50 bg-[#0F172A] backdrop-blur-md">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {onBack && (
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={onBack}
                className="md:hidden text-purple-300 hover:bg-purple-800/50"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
            )}
            <Avatar className="h-10 w-10 border border-purple-500/30">
              <AvatarImage src={chatInfo.avatar || ''} alt={chatInfo.name} />
              <AvatarFallback className="bg-purple-900/50 text-purple-200">
                {chatInfo.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center space-x-2">
                <h2 className="font-semibold text-white">{chatInfo.name}</h2>
                {chatInfo.isOnline && (
                  <div className="relative">
                    <span className="flex h-2 w-2">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                    </span>
                  </div>
                )}
              </div>
              <p className="text-xs text-purple-200/70">
                {chatInfo.isOnline ? 'Online' : chatInfo.lastSeen}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            {chatInfo.isEncrypted && (
              <Badge 
                variant="outline" 
                className="bg-purple-900/30 border-purple-700/50 text-purple-200 text-xs px-2 py-0.5"
              >
                <Shield className="h-3 w-3 mr-1 text-purple-300" />
                Encrypted
              </Badge>
            )}
            <Button variant="ghost" size="icon" className="text-purple-300 hover:bg-purple-800/50">
              <Phone className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="text-purple-300 hover:bg-purple-800/50">
              <Video className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="text-purple-300 hover:bg-purple-800/50">
              <MoreVertical className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-[#1E1B4B] to-[#0F172A]">
        <div className="max-w-4xl mx-auto w-full space-y-4">
          {renderMessages()}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Message Input */}
      <div className="border-t border-purple-900/50 p-4 bg-[#0F172A]">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="text-purple-300 hover:bg-purple-800/50">
            <Paperclip className="h-5 w-5" />
          </Button>
          <div className="flex-1 relative">
            <Input
              placeholder="Type a message..."
              className="bg-gray-800/50 border-gray-700 text-white placeholder-gray-400 focus-visible:ring-purple-500/50 pr-12"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
            />
            <Button 
              variant="ghost" 
              size="icon" 
              className="absolute right-1 top-1/2 transform -translate-y-1/2 text-purple-300 hover:bg-purple-800/50"
            >
              <Smile className="h-5 w-5" />
            </Button>
          </div>
          <Button 
            size="icon"
            onClick={handleSendMessage}
            disabled={!message.trim()}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            <Send className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  );
}
