'use client';

import { formatMessageTime, formatCurrency } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import { Check, CheckCheck, DollarSign, Bitcoin } from 'lucide-react';

interface MessageBubbleProps {
  message: {
    id: string;
    senderId: string;
    senderName: string;
    content: string;
    timestamp: Date;
    messageType: 'TEXT' | 'IMAGE' | 'FILE' | 'AUDIO' | 'VIDEO' | 'PAYMENT' | 'CRYPTO_PAYMENT';
    paymentData?: {
      amount: number;
      currency: string;
      status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
    };
    cryptoData?: {
      amount: string;
      currency: string;
      network: string;
      status: 'PENDING' | 'CONFIRMED' | 'FAILED';
    };
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
    isDelivered: boolean;
    isRead: boolean;
  };
  isOwn: boolean;
}

export function MessageBubble({ message, isOwn }: MessageBubbleProps) {
  const renderMessageContent = () => {
    switch (message.messageType) {
      case 'TEXT':
        return (
          <p className="text-sm whitespace-pre-wrap break-words">
            {message.content}
          </p>
        );

      case 'PAYMENT':
        if (!message.paymentData) return null;
        return (
          <div className="flex items-center space-x-3 p-3 bg-green-500/10 rounded-lg border border-green-500/20">
            <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="payment-amount">
                  {formatCurrency(message.paymentData.amount, message.paymentData.currency)}
                </span>
                <Badge 
                  variant={message.paymentData.status === 'COMPLETED' ? 'default' : 'secondary'}
                  className={
                    message.paymentData.status === 'COMPLETED' 
                      ? 'bg-green-500 text-white' 
                      : message.paymentData.status === 'FAILED'
                      ? 'bg-red-500 text-white'
                      : 'bg-yellow-500 text-white'
                  }
                >
                  {message.paymentData.status}
                </Badge>
              </div>
              <p className="text-xs text-gray-400 mt-1">
                {isOwn ? 'Payment sent' : 'Payment received'}
              </p>
            </div>
          </div>
        );

      case 'CRYPTO_PAYMENT':
        if (!message.cryptoData) return null;
        return (
          <div className="flex items-center space-x-3 p-3 bg-orange-500/10 rounded-lg border border-orange-500/20">
            <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
              <Bitcoin className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="crypto-amount">
                  {message.cryptoData.amount} {message.cryptoData.currency}
                </span>
                <Badge 
                  variant={message.cryptoData.status === 'CONFIRMED' ? 'default' : 'secondary'}
                  className={
                    message.cryptoData.status === 'CONFIRMED' 
                      ? 'bg-green-500 text-white' 
                      : message.cryptoData.status === 'FAILED'
                      ? 'bg-red-500 text-white'
                      : 'bg-yellow-500 text-white'
                  }
                >
                  {message.cryptoData.status}
                </Badge>
              </div>
              <p className="text-xs text-gray-400 mt-1">
                {isOwn ? 'Crypto sent' : 'Crypto received'} • {message.cryptoData.network}
              </p>
            </div>
          </div>
        );

      case 'IMAGE':
        return (
          <div className="max-w-sm">
            <img
              src={message.fileUrl}
              alt="Shared image"
              className="rounded-lg max-w-full h-auto"
            />
            {message.content && (
              <p className="text-sm mt-2 whitespace-pre-wrap break-words">
                {message.content}
              </p>
            )}
          </div>
        );

      case 'FILE':
        return (
          <div className="flex items-center space-x-3 p-3 bg-gray-700/50 rounded-lg border border-gray-600">
            <div className="w-10 h-10 bg-gray-600 rounded-lg flex items-center justify-center">
              <span className="text-xs font-medium text-white">
                {message.fileName?.split('.').pop()?.toUpperCase() || 'FILE'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">{message.fileName}</p>
              <p className="text-xs text-gray-400">
                {message.fileSize ? `${(message.fileSize / 1024 / 1024).toFixed(1)} MB` : 'Unknown size'}
              </p>
            </div>
          </div>
        );

      default:
        return (
          <p className="text-sm text-gray-400 italic">
            Unsupported message type
          </p>
        );
    }
  };

  return (
    <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} animate-message-in`}>
      <div className={`max-w-xs lg:max-w-md ${isOwn ? 'order-2' : 'order-1'}`}>
        <div
          className={`px-4 py-2 ${
            isOwn
              ? 'message-bubble-sent'
              : 'message-bubble-received'
          }`}
        >
          {renderMessageContent()}
        </div>
        
        <div className={`flex items-center mt-1 space-x-1 ${isOwn ? 'justify-end' : 'justify-start'}`}>
          <span className="text-xs text-gray-400">
            {formatMessageTime(message.timestamp)}
          </span>
          
          {isOwn && (
            <div className="flex items-center">
              {message.isRead ? (
                <CheckCheck className="w-3 h-3 text-blue-400" />
              ) : message.isDelivered ? (
                <CheckCheck className="w-3 h-3 text-gray-400" />
              ) : (
                <Check className="w-3 h-3 text-gray-400" />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
