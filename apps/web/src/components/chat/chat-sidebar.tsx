'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useAuthStore } from '@/store/auth';
import { 
  Search, 
  Plus, 
  MessageCircle, 
  Settings, 
  CreditCard,
  Users,
  MoreVertical,
  LogOut
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface ChatSidebarProps {
  selectedChatId: string | null;
  onChatSelect: (chatId: string) => void;
}

export function ChatSidebar({ selectedChatId, onChatSelect }: ChatSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const { user, logout } = useAuthStore();

  // Mock chat data - in real app this would come from API/store
  const chats = [
    {
      id: '1',
      name: '<PERSON>',
      lastMessage: 'Hey! How are you doing?',
      timestamp: '2m ago',
      unreadCount: 2,
      avatar: null,
      isOnline: true,
      isGroup: false,
    },
    {
      id: '2',
      name: 'Bob Smith',
      lastMessage: 'Thanks for the payment! 💰',
      timestamp: '1h ago',
      unreadCount: 0,
      avatar: null,
      isOnline: false,
      isGroup: false,
    },
    {
      id: '3',
      name: 'Team Chat',
      lastMessage: 'Meeting at 3 PM today',
      timestamp: '3h ago',
      unreadCount: 5,
      avatar: null,
      isOnline: false,
      isGroup: true,
    },
  ];

  const handleLogout = async () => {
    await logout();
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const formatTime = (timestamp: string) => {
    // In real app, this would format actual timestamps
    return timestamp;
  };

  return (
    <div className="h-full flex flex-col bg-gray-800">
      {/* Header */}
      <div className="p-4 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Avatar className="w-12 h-12 ring-2 ring-purple-500/20">
              <AvatarImage src={user?.avatar || ''} />
              <AvatarFallback className="bg-gradient-to-br from-purple-600 to-purple-700 text-white font-semibold">
                {user?.displayName ? getInitials(user.displayName) : 'U'}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h2 className="font-semibold text-white text-lg">{user?.displayName}</h2>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <p className="text-sm text-green-400">Online</p>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-gray-700 rounded-full">
                <MoreVertical className="w-5 h-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700 shadow-xl">
              <DropdownMenuItem className="text-gray-300 hover:bg-gray-700 focus:bg-gray-700">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem className="text-gray-300 hover:bg-gray-700 focus:bg-gray-700">
                <CreditCard className="w-4 h-4 mr-2" />
                Payments
              </DropdownMenuItem>
              <DropdownMenuItem className="text-gray-300 hover:bg-gray-700 focus:bg-gray-700">
                <Users className="w-4 h-4 mr-2" />
                Contacts
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-gray-700" />
              <DropdownMenuItem
                className="text-red-400 hover:bg-red-500/10 focus:bg-red-500/10"
                onClick={handleLogout}
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search or start new chat"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-12 pr-4 py-3 bg-gray-700 border-gray-600 text-white placeholder-gray-400 rounded-xl focus:bg-gray-600 focus:border-purple-500 transition-all duration-200"
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="p-4 border-b border-gray-700">
        <div className="grid grid-cols-2 gap-2">
          <Button variant="outline" size="sm" className="border-gray-600 text-gray-300 hover:bg-gray-800">
            <Plus className="w-4 h-4 mr-2" />
            New Chat
          </Button>
          <Button variant="outline" size="sm" className="border-gray-600 text-gray-300 hover:bg-gray-800">
            <Users className="w-4 h-4 mr-2" />
            Contacts
          </Button>
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        {chats
          .filter(chat => 
            chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            chat.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
          )
          .map((chat) => (
            <div
              key={chat.id}
              onClick={() => onChatSelect(chat.id)}
              className={`p-4 cursor-pointer transition-all duration-200 hover:bg-gray-700/50 ${
                selectedChatId === chat.id
                  ? 'bg-purple-600/20 border-r-4 border-purple-500'
                  : 'border-b border-gray-700/50'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar className="w-14 h-14 ring-2 ring-gray-600">
                    <AvatarImage src={chat.avatar || ''} />
                    <AvatarFallback className={`text-white font-semibold ${
                      chat.isGroup
                        ? 'bg-gradient-to-br from-blue-600 to-blue-700'
                        : 'bg-gradient-to-br from-purple-600 to-purple-700'
                    }`}>
                      {chat.isGroup ? (
                        <Users className="w-7 h-7" />
                      ) : (
                        getInitials(chat.name)
                      )}
                    </AvatarFallback>
                  </Avatar>
                  {chat.isOnline && !chat.isGroup && (
                    <div className="absolute bottom-0 right-0 w-4 h-4 bg-green-400 border-2 border-gray-800 rounded-full"></div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-semibold text-white truncate text-lg">
                      {chat.name}
                    </h3>
                    <span className="text-xs text-gray-400 font-medium">
                      {formatTime(chat.timestamp)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-300 truncate leading-relaxed">
                      {chat.lastMessage}
                    </p>
                    {chat.unreadCount > 0 && (
                      <div className="min-w-[20px] h-5 bg-purple-600 text-white text-xs rounded-full flex items-center justify-center px-2 font-medium">
                        {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center justify-center space-x-2 text-gray-400">
          <MessageCircle className="w-4 h-4" />
          <span className="text-sm">ChatPay v1.0</span>
        </div>
      </div>
    </div>
  );
}
