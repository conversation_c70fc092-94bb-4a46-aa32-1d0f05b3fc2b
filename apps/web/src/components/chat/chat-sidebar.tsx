'use client';

import { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useAuthStore } from '@/store/auth';
import { 
  Search, 
  Plus, 
  MessageCircle, 
  Settings, 
  CreditCard,
  Users,
  MoreVertical,
  LogOut,
  ChevronLeft,
  UserPlus,
  UserCheck,
  ShieldCheck,
  ArrowLeft
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface ChatSidebarProps {
  selectedChatId: string | null;
  onChatSelect: (chatId: string) => void;
  onBack?: () => void;
  isMobile?: boolean;
}

export function ChatS<PERSON>bar({ selectedChatId, onChatSelect, onBack, isMobile }: ChatSidebarProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const { user, logout } = useAuthStore();

  interface Chat {
    id: string;
    name: string;
    lastMessage: string;
    timestamp: string;
    unreadCount: number;
    avatar: string | null;
    isOnline: boolean;
    isGroup: boolean;
    isEncrypted: boolean;
    lastSeen: string;
    members: number;
  }

  // Mock chat data - in real app this would come from API/store
  const chats = useMemo<Chat[]>(() => [
    {
      id: '1',
      name: 'Alice Johnson',
      lastMessage: 'Hey! How are you doing?',
      timestamp: '2m ago',
      unreadCount: 2,
      avatar: null,
      isOnline: true,
      isGroup: false,
      isEncrypted: true,
      lastSeen: '2m ago',
      members: 0
    },
    {
      id: '2',
      name: 'Bob Smith',
      lastMessage: 'Thanks for the payment! 💰',
      timestamp: '1h ago',
      unreadCount: 0,
      avatar: null,
      isOnline: false,
      isGroup: false,
      isEncrypted: true,
      lastSeen: '1h ago',
      members: 0
    },
    {
      id: '3',
      name: 'Crypto Traders',
      lastMessage: 'John: Just bought more BTC!',
      timestamp: '3h ago',
      unreadCount: 5,
      avatar: null,
      isOnline: false,
      isGroup: true,
      members: 12,
      isEncrypted: true,
      lastSeen: ''
    },
    {
      id: '4',
      name: 'Team Chat',
      lastMessage: 'Meeting at 3pm tomorrow',
      timestamp: '5h ago',
      unreadCount: 0,
      avatar: null,
      isOnline: false,
      isGroup: true,
      isEncrypted: true,
      lastSeen: '',
      members: 5
    }
  ], []);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const formatTime = (timestamp: string) => {
    // Format timestamp as needed
    return timestamp;
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n: string) => n[0])
      .join('')
      .toUpperCase();
  };

  const filteredChats = useMemo(() => {
    if (!searchQuery.trim()) return chats;
    const query = searchQuery.toLowerCase();
    return chats.filter(chat => 
      chat.name.toLowerCase().includes(query) || 
      chat.lastMessage?.toLowerCase().includes(query)
    );
  }, [chats, searchQuery]);

  return (
    <div className="h-full flex flex-col bg-[hsl(270,15%,10%)] border-r border-[hsl(270,10%,15%)]">
      {/* Header */}
      <div className="p-3 border-b border-[hsl(270,10%,15%)] bg-[hsl(270,15%,12%)] flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1">
          {isMobile && onBack && (
            <Button 
              variant="ghost" 
              size="icon" 
              className="text-gray-400 hover:text-white hover:bg-[hsl(270,15%,15%)]"
              onClick={onBack}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
          )}
          <Avatar className="h-10 w-10 border-2 border-[hsl(262,83%,65%)]">
            <AvatarImage src={user?.avatar || ''} alt={user?.displayName || 'User'} />
            <AvatarFallback className="bg-[hsl(262,83%,40%)] text-white">
              {user?.displayName?.charAt(0) || user?.email?.charAt(0).toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <h2 className="font-semibold text-white truncate">{user?.displayName || 'User'}</h2>
            <div className="flex items-center space-x-1">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <p className="text-xs text-gray-400 truncate">Online</p>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <Button 
            variant="ghost" 
            size="icon" 
            className="text-gray-400 hover:text-white hover:bg-[hsl(270,15%,15%)]"
            onClick={() => {/* New chat */}}
          >
            <MessageCircle className="h-5 w-5" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                className="text-gray-400 hover:text-white hover:bg-[hsl(270,15%,15%)]"
              >
                <MoreVertical className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
              className="w-56 bg-[hsl(270,15%,12%)] border border-[hsl(270,10%,20%)] text-white shadow-xl"
              sideOffset={5}
              align="end"
            >
              <DropdownMenuItem 
                className="flex items-center space-x-3 hover:bg-[hsl(270,15%,18%)] cursor-pointer px-3 py-2.5"
                onClick={() => {/* New group */}}
              >
                <Users className="h-4 w-4 text-[hsl(262,83%,65%)]" />
                <span>New Group</span>
              </DropdownMenuItem>
              <DropdownMenuItem 
                className="flex items-center space-x-3 hover:bg-[hsl(270,15%,18%)] cursor-pointer px-3 py-2.5"
                onClick={() => {/* New contact */}}
              >
                <UserPlus className="h-4 w-4 text-[hsl(262,83%,65%)]" />
                <span>New Contact</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-[hsl(270,10%,20%)]" />
              <DropdownMenuItem 
                className="flex items-center space-x-3 hover:bg-[hsl(270,15%,18%)] cursor-pointer px-3 py-2.5"
                onClick={() => router.push('/settings')}
              >
                <Settings className="h-4 w-4 text-gray-400" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem 
                className="flex items-center space-x-3 hover:bg-[hsl(270,15%,18%)] cursor-pointer px-3 py-2.5"
                onClick={() => router.push('/payments')}
              >
                <CreditCard className="h-4 w-4 text-amber-400" />
                <span>Payments</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-[hsl(270,10%,20%)]" />
              <DropdownMenuItem 
                className="flex items-center space-x-3 text-red-400 hover:bg-red-900/20 cursor-pointer px-3 py-2.5"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Search */}
      <div className="p-3 border-b border-[hsl(270,10%,15%)] bg-[hsl(270,15%,12%)]">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search or start new chat"
            className="pl-10 bg-[hsl(270,15%,15%)] border-[hsl(270,10%,20%)] text-white placeholder-gray-500 
                      focus:border-[hsl(262,83%,65%)] focus-visible:ring-1 focus-visible:ring-[hsl(262,83%,65%)] 
                      transition-colors duration-200 h-10 text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <button 
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-300"
              onClick={() => setSearchQuery('')}
            >
              ✕
            </button>
          )}
        </div>
      </div>

      {/* Chats List */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        {filteredChats.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center p-6">
            <MessageCircle className="h-12 w-12 text-gray-500 mb-3" />
            <h3 className="text-gray-400 font-medium">No chats found</h3>
            <p className="text-gray-500 text-sm mt-1">
              {searchQuery ? 'Try a different search' : 'Start a new conversation'}
            </p>
            {!searchQuery && (
              <Button className="mt-4 bg-[hsl(262,83%,65%)] hover:bg-[hsl(262,83%,60%)] text-white">
                <MessageCircle className="h-4 w-4 mr-2" />
                New Chat
              </Button>
            )}
          </div>
        ) : (
          filteredChats.map((chat) => (
            <div
              key={chat.id}
              className={`flex items-center p-3 border-b border-[hsl(270,10%,15%)] cursor-pointer transition-colors duration-200
                ${selectedChatId === chat.id 
                  ? 'bg-[hsl(262,83%,20%)]' 
                  : 'hover:bg-[hsl(270,15%,15%)]'}`}
              onClick={() => onChatSelect(chat.id)}
            >
              <div className="relative">
                <Avatar className={`h-12 w-12 ${chat.isGroup ? 'ring-2 ring-amber-400' : ''}`}>
                  <AvatarImage src={chat.avatar || ''} alt={chat.name} />
                  <AvatarFallback className="bg-[hsl(262,83%,40%)] text-white">
                    {chat.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
                {!chat.isGroup && chat.isOnline && (
                  <div className="absolute bottom-0 right-0 h-3 w-3 bg-green-500 rounded-full border-2 border-[hsl(270,15%,10%)]"></div>
                )}
                {chat.isGroup && (
                  <div className="absolute -bottom-1 -right-1 bg-[hsl(270,15%,10%)] p-0.5 rounded-full">
                    <Users className="h-3 w-3 text-amber-400" />
                  </div>
                )}
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <h3 className="font-medium text-white truncate max-w-[120px]">
                      {chat.name}
                    </h3>
                    {chat.isEncrypted && (
                      <ShieldCheck className="h-3.5 w-3.5 ml-1.5 text-[hsl(262,83%,65%)]" />
                    )}
                  </div>
                  <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                    {chat.timestamp}
                  </span>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <div className="flex items-center flex-1 min-w-0">
                    {chat.lastMessage && (
                      <p className="text-sm text-gray-400 truncate max-w-[180px]">
                        {chat.lastMessage}
                      </p>
                    )}
                    {chat.isGroup && chat.members && (
                      <span className="text-xs text-gray-500 ml-1.5 whitespace-nowrap">
                        • {chat.members} members
                      </span>
                    )}
                  </div>
                  {chat.unreadCount > 0 && (
                    <div className="flex items-center ml-2">
                      <Badge className="bg-[hsl(262,83%,65%)] text-white rounded-full h-5 min-w-5 flex items-center justify-center p-0 text-xs">
                        {chat.unreadCount > 9 ? '9+' : chat.unreadCount}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="p-2 border-t border-[hsl(270,10%,15%)] bg-[hsl(270,15%,12%)]">
        <div className="flex items-center justify-around">
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-[hsl(270,15%,15%)]">
            <MessageCircle className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-[hsl(270,15%,15%)]">
            <Users className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-[hsl(270,15%,15%)]">
            <CreditCard className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-[hsl(270,15%,15%)]">
            <Settings className="h-5 w-5" />
          </Button>
        </div>
      </div>
      {/* Footer */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center justify-center space-x-2 text-gray-400">
          <MessageCircle className="w-4 h-4" />
          <span className="text-sm">ChatPay v1.0</span>
        </div>
      </div>
    </div>
  );
}
