'use client';

import { useState } from 'react';
import { ChatSidebar } from './chat-sidebar';
import { ChatWindow } from './chat-window';
import { WelcomeScreen } from './welcome-screen';

export function ChatLayout() {
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);

  return (
    <div className="h-screen flex bg-gradient-dark">
      {/* Sidebar */}
      <div className="w-80 border-r border-gray-700 flex-shrink-0">
        <ChatSidebar
          selectedChatId={selectedChatId}
          onChatSelect={setSelectedChatId}
        />
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedChatId ? (
          <ChatWindow chatId={selectedChatId} />
        ) : (
          <WelcomeScreen />
        )}
      </div>
    </div>
  );
}
