'use client';

import { MessageCircle, Shield, CreditCard, Zap } from 'lucide-react';
import { useAuthStore } from '@/store/auth';

export function WelcomeScreen() {
  const { user } = useAuthStore();

  const features = [
    {
      icon: MessageCircle,
      title: 'Secure Messaging',
      description: 'End-to-end encrypted conversations',
      color: 'text-primary-500'
    },
    {
      icon: Shield,
      title: 'Privacy First',
      description: 'Your data stays private and secure',
      color: 'text-green-500'
    },
    {
      icon: CreditCard,
      title: 'Instant Payments',
      description: 'Send money with just a message',
      color: 'text-accent-500'
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Real-time messaging and payments',
      color: 'text-blue-500'
    }
  ];

  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="max-w-2xl text-center">
        {/* Logo and Welcome */}
        <div className="mb-8">
          <div className="w-20 h-20 bg-gradient-purple-gold rounded-2xl flex items-center justify-center mx-auto mb-6">
            <MessageCircle className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-white mb-2">
            Welcome to ChatPay, {user?.displayName}!
          </h1>
          <p className="text-xl text-gray-300">
            Select a chat to start messaging or create a new conversation
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-2 gap-6 mb-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-gray-800/50 rounded-xl p-6 border border-gray-700 hover:bg-gray-800/70 transition-colors"
            >
              <feature.icon className={`w-8 h-8 ${feature.color} mb-3 mx-auto`} />
              <h3 className="text-lg font-semibold text-white mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-400 text-sm">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="text-gray-400">
          <p className="mb-4">Get started by:</p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <span className="bg-gray-800 px-3 py-1 rounded-full">
              💬 Starting a new chat
            </span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">
              👥 Adding contacts
            </span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">
              💳 Setting up payments
            </span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">
              🔐 Enabling encryption
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
