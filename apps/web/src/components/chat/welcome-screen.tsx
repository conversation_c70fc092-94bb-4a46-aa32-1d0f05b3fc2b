'use client';

import { MessageCircle, Shield, CreditCard, Zap } from 'lucide-react';
import { useAuthStore } from '@/store/auth';

export function WelcomeScreen() {
  const { user } = useAuthStore();

  const features = [
    {
      icon: MessageCircle,
      title: 'Secure Messaging',
      description: 'End-to-end encrypted conversations',
      color: 'text-primary-500'
    },
    {
      icon: Shield,
      title: 'Privacy First',
      description: 'Your data stays private and secure',
      color: 'text-green-500'
    },
    {
      icon: CreditCard,
      title: 'Instant Payments',
      description: 'Send money with just a message',
      color: 'text-accent-500'
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Real-time messaging and payments',
      color: 'text-blue-500'
    }
  ];

  return (
    <div className="flex-1 flex items-center justify-center p-8 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="max-w-2xl text-center">
        {/* Logo and Welcome */}
        <div className="mb-12">
          <div className="relative mb-8">
            <div className="w-32 h-32 bg-gradient-to-br from-purple-600 via-purple-500 to-amber-500 rounded-3xl flex items-center justify-center mx-auto shadow-2xl">
              <MessageCircle className="w-16 h-16 text-white" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-400 rounded-full flex items-center justify-center shadow-lg">
              <Shield className="w-4 h-4 text-white" />
            </div>
            <div className="absolute -bottom-2 -left-2 w-8 h-8 bg-amber-400 rounded-full flex items-center justify-center shadow-lg">
              <CreditCard className="w-4 h-4 text-white" />
            </div>
          </div>
          <h1 className="text-5xl font-bold mb-4">
            <span className="text-white">Welcome back,</span>
            <br />
            <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">
              {user?.displayName}!
            </span>
          </h1>
          <p className="text-xl text-gray-300 leading-relaxed">
            Select a chat to start messaging or create a new conversation with end-to-end encryption
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-2 gap-8 mb-12">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group bg-gray-800/30 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 hover:bg-gray-800/50 hover:border-gray-600 transition-all duration-300 hover:scale-105"
            >
              <div className={`w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4 bg-gradient-to-br ${
                feature.color === 'text-primary-500' ? 'from-purple-500 to-purple-600' :
                feature.color === 'text-green-500' ? 'from-green-500 to-green-600' :
                feature.color === 'text-accent-500' ? 'from-amber-500 to-amber-600' :
                'from-blue-500 to-blue-600'
              } shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                <feature.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-purple-300 transition-colors duration-300">
                {feature.title}
              </h3>
              <p className="text-gray-300 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="text-gray-400">
          <p className="mb-4">Get started by:</p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <span className="bg-gray-800 px-3 py-1 rounded-full">
              💬 Starting a new chat
            </span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">
              👥 Adding contacts
            </span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">
              💳 Setting up payments
            </span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">
              🔐 Enabling encryption
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
