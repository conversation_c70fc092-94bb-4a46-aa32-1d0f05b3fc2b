'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { AuthModal } from '@/components/auth/auth-modal';
import { ChatPreview } from '@/components/landing/chat-preview';
import {
  MessageCircle,
  Shield,
  CreditCard,
  Smartphone,
  Globe,
  Zap,
  Lock,
  DollarSign,
  Users,
  Star,
  ArrowRight,
  Play
} from 'lucide-react';

export function LandingPage() {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  const handleGetStarted = () => {
    setAuthMode('register');
    setShowAuthModal(true);
  };

  const handleSignIn = () => {
    setAuthMode('login');
    setShowAuthModal(true);
  };

  const features = [
    {
      icon: MessageCircle,
      title: 'Secure Messaging',
      description: 'End-to-end encrypted chat with Signal Protocol security',
      color: 'text-primary-500'
    },
    {
      icon: CreditCard,
      title: 'Instant Payments',
      description: 'Send money instantly with bank integration via Plaid',
      color: 'text-accent-500'
    },
    {
      icon: Shield,
      title: 'Military-Grade Security',
      description: 'Your messages and payments are protected with top-tier encryption',
      color: 'text-green-500'
    },
    {
      icon: Smartphone,
      title: 'Cross-Platform',
      description: 'Available on web, iOS, and Android with seamless sync',
      color: 'text-blue-500'
    },
    {
      icon: DollarSign,
      title: 'Crypto Support',
      description: 'Send and receive cryptocurrency with built-in wallet',
      color: 'text-yellow-500'
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Real-time messaging and instant payment processing',
      color: 'text-purple-500'
    }
  ];

  const stats = [
    { label: 'Messages Encrypted', value: '10M+', icon: Lock },
    { label: 'Payments Processed', value: '$50M+', icon: DollarSign },
    { label: 'Active Users', value: '100K+', icon: Users },
    { label: 'Security Rating', value: '5/5', icon: Star },
  ];

  return (
    <div className="min-h-screen bg-gray-900 overflow-hidden">
      {/* Header */}
      <header className="relative z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <MessageCircle className="w-6 h-6 text-white" />
            </div>
            <div>
              <span className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-purple-300 bg-clip-text text-transparent">
                ChatPay
              </span>
              <div className="text-xs text-gray-400 -mt-1">Secure • Private • Instant</div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              onClick={handleSignIn}
              className="text-gray-300 hover:text-white hover:bg-gray-800 transition-all duration-200"
            >
              Sign In
            </Button>
            <Button
              onClick={handleGetStarted}
              className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-6 py-2 rounded-xl font-medium shadow-lg hover:shadow-purple-500/25 transition-all duration-200"
            >
              Get Started
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section with Chat Preview */}
      <section className="relative">
        <div className="container mx-auto px-6 py-16">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8">
              <div className="inline-flex items-center space-x-2 bg-purple-500/10 border border-purple-500/20 rounded-full px-4 py-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-purple-300 font-medium">Now with Crypto Payments</span>
              </div>

              <div className="space-y-6">
                <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                  <span className="text-white">Secure Chat</span>
                  <br />
                  <span className="bg-gradient-to-r from-purple-400 via-purple-300 to-amber-300 bg-clip-text text-transparent">
                    Instant Payments
                  </span>
                </h1>

                <p className="text-xl text-gray-300 leading-relaxed max-w-lg">
                  The most secure messaging app with built-in payments. Send encrypted messages and transfer money instantly with bank-grade security.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  onClick={handleGetStarted}
                  className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-purple-500/25 transition-all duration-200"
                >
                  <MessageCircle className="w-5 h-5 mr-2" />
                  Start Chatting
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-800 hover:border-gray-500 px-8 py-4 rounded-xl font-medium transition-all duration-200"
                >
                  <Play className="w-5 h-5 mr-2" />
                  Watch Demo
                </Button>
              </div>

              {/* Stats */}
              <div className="flex items-center space-x-8 pt-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">10M+</div>
                  <div className="text-sm text-gray-400">Messages Encrypted</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">$50M+</div>
                  <div className="text-sm text-gray-400">Payments Processed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">100K+</div>
                  <div className="text-sm text-gray-400">Active Users</div>
                </div>
              </div>
            </div>

            {/* Right - Chat Preview */}
            <div className="relative">
              <ChatPreview />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-800/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Everything You Need in One App
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              ChatPay combines the best of secure messaging with seamless payment processing
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="group bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-6 hover:bg-gray-800/70 hover:border-gray-600 transition-all duration-300 hover:scale-105"
              >
                <div className="flex items-center space-x-4 mb-4">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center bg-gradient-to-br ${
                    feature.color === 'text-primary-500' ? 'from-purple-500 to-purple-600' :
                    feature.color === 'text-accent-500' ? 'from-amber-500 to-amber-600' :
                    feature.color === 'text-green-500' ? 'from-green-500 to-green-600' :
                    feature.color === 'text-blue-500' ? 'from-blue-500 to-blue-600' :
                    feature.color === 'text-yellow-500' ? 'from-yellow-500 to-yellow-600' :
                    'from-purple-500 to-purple-600'
                  } shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white group-hover:text-purple-300 transition-colors duration-300">
                    {feature.title}
                  </h3>
                </div>
                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Everything You Need in One App
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              ChatPay combines the best of secure messaging with seamless payment processing
            </p>
          </div>

        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-4 bg-gray-900/50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-300">
              Get started in minutes with our simple 3-step process
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">1</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Sign Up</h3>
              <p className="text-gray-300">Create your account with email or phone number</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-accent-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">2</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Connect Bank</h3>
              <p className="text-gray-300">Securely link your bank account with Plaid</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">3</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Start Chatting</h3>
              <p className="text-gray-300">Send messages and money to friends instantly</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Experience the Future?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of users who are already chatting and paying securely with ChatPay
            </p>
            <Button size="lg" onClick={handleGetStarted} className="chatpay-gradient text-lg px-12 py-4">
              Get Started Free
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-gray-800 py-8 px-4">
        <div className="container mx-auto text-center text-gray-400">
          <p>&copy; 2024 ChatPay. All rights reserved. Built with security and privacy in mind.</p>
        </div>
      </footer>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode={authMode}
        onModeChange={setAuthMode}
      />
    </div>
  );
}
