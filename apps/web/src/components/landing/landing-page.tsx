'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AuthModal } from '@/components/auth/auth-modal';
import { 
  MessageCircle, 
  Shield, 
  CreditCard, 
  Smartphone, 
  Globe, 
  Zap,
  Lock,
  DollarSign,
  Users,
  Star
} from 'lucide-react';

export function LandingPage() {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  const handleGetStarted = () => {
    setAuthMode('register');
    setShowAuthModal(true);
  };

  const handleSignIn = () => {
    setAuthMode('login');
    setShowAuthModal(true);
  };

  const features = [
    {
      icon: MessageCircle,
      title: 'Secure Messaging',
      description: 'End-to-end encrypted chat with Signal Protocol security',
      color: 'text-primary-500'
    },
    {
      icon: CreditCard,
      title: 'Instant Payments',
      description: 'Send money instantly with bank integration via Plaid',
      color: 'text-accent-500'
    },
    {
      icon: Shield,
      title: 'Military-Grade Security',
      description: 'Your messages and payments are protected with top-tier encryption',
      color: 'text-green-500'
    },
    {
      icon: Smartphone,
      title: 'Cross-Platform',
      description: 'Available on web, iOS, and Android with seamless sync',
      color: 'text-blue-500'
    },
    {
      icon: DollarSign,
      title: 'Crypto Support',
      description: 'Send and receive cryptocurrency with built-in wallet',
      color: 'text-yellow-500'
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Real-time messaging and instant payment processing',
      color: 'text-purple-500'
    }
  ];

  const stats = [
    { label: 'Messages Encrypted', value: '10M+', icon: Lock },
    { label: 'Payments Processed', value: '$50M+', icon: DollarSign },
    { label: 'Active Users', value: '100K+', icon: Users },
    { label: 'Security Rating', value: '5/5', icon: Star },
  ];

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Header */}
      <header className="border-b border-gray-800">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-purple-gold rounded-lg flex items-center justify-center">
              <MessageCircle className="w-5 h-5 text-white" />
            </div>
            <span className="text-2xl font-bold chatpay-gradient-text">ChatPay</span>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={handleSignIn}>
              Sign In
            </Button>
            <Button onClick={handleGetStarted} className="chatpay-gradient">
              Get Started
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Badge className="mb-6 bg-primary-500/10 text-primary-400 border-primary-500/20">
            🚀 Now with Crypto Payments
          </Badge>
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Secure Chat Meets
            <br />
            <span className="chatpay-gradient-text">Instant Payments</span>
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            The future of communication is here. Send encrypted messages and transfer money 
            instantly with bank-grade security. All in one beautiful app.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" onClick={handleGetStarted} className="chatpay-gradient text-lg px-8 py-4">
              Start Chatting & Paying
            </Button>
            <Button size="lg" variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800">
              <Globe className="w-5 h-5 mr-2" />
              Try Web Version
            </Button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 border-y border-gray-800">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-2">
                  <stat.icon className="w-8 h-8 text-accent-500" />
                </div>
                <div className="text-3xl font-bold text-white mb-1">{stat.value}</div>
                <div className="text-gray-400">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Everything You Need in One App
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              ChatPay combines the best of secure messaging with seamless payment processing
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-colors">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <feature.icon className={`w-8 h-8 ${feature.color}`} />
                    <CardTitle className="text-white">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-300">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-4 bg-gray-900/50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-300">
              Get started in minutes with our simple 3-step process
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">1</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Sign Up</h3>
              <p className="text-gray-300">Create your account with email or phone number</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-accent-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">2</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Connect Bank</h3>
              <p className="text-gray-300">Securely link your bank account with Plaid</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">3</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Start Chatting</h3>
              <p className="text-gray-300">Send messages and money to friends instantly</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Experience the Future?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of users who are already chatting and paying securely with ChatPay
            </p>
            <Button size="lg" onClick={handleGetStarted} className="chatpay-gradient text-lg px-12 py-4">
              Get Started Free
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-gray-800 py-8 px-4">
        <div className="container mx-auto text-center text-gray-400">
          <p>&copy; 2024 ChatPay. All rights reserved. Built with security and privacy in mind.</p>
        </div>
      </footer>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode={authMode}
        onModeChange={setAuthMode}
      />
    </div>
  );
}
