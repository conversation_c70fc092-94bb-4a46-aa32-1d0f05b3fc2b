import { apiClient } from './client';
import { User, AuthTokens, LoginRequest, RegisterRequest } from '@chatpay/shared';

export const authApi = {
  login: (data: LoginRequest) =>
    apiClient.post<{ user: User; tokens: AuthTokens }>('/auth/login', data),

  register: (data: RegisterRequest) =>
    apiClient.post<{ user: User; tokens: AuthTokens }>('/auth/register', data),

  authenticateWithFirebase: (data: { idToken: string }) =>
    apiClient.post<{ user: User; tokens: AuthTokens }>('/auth/firebase', data),

  refreshToken: (data: { refreshToken: string }) =>
    apiClient.post<AuthTokens>('/auth/refresh', data),

  logout: () =>
    apiClient.post('/auth/logout'),

  getProfile: () =>
    apiClient.get<User>('/auth/me'),
};
