import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, AuthTokens } from '@/lib/types';
import { authApi } from '@/lib/api/auth';
import { setAuthToken, removeAuthToken } from '@/lib/api/client';

interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>;
  register: (data: {
    email: string;
    password: string;
    username: string;
    displayName: string;
    phoneNumber?: string;
  }) => Promise<void>;
  loginWithFirebase: (idToken: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  checkAuth: () => Promise<void>;
  setUser: (user: User) => void;
  clearAuth: () => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      tokens: null,
      isLoading: false,
      isAuthenticated: false,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true });
        try {
          // Mock authentication for demo
          await new Promise(resolve => setTimeout(resolve, 1000));

          const mockUser: User = {
            id: '1',
            email,
            username: email.split('@')[0],
            displayName: 'Demo User',
            avatar: '',
            phoneNumber: '+1234567890',
            isVerified: true,
            isOnline: true,
            lastSeen: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          const mockTokens: AuthTokens = {
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
            expiresIn: 3600,
          };

          set({
            user: mockUser,
            tokens: mockTokens,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      register: async (data) => {
        set({ isLoading: true });
        try {
          // Mock registration for demo
          await new Promise(resolve => setTimeout(resolve, 1000));

          const mockUser: User = {
            id: '1',
            email: data.email,
            username: data.username,
            displayName: data.displayName,
            avatar: '',
            phoneNumber: data.phoneNumber,
            isVerified: true,
            isOnline: true,
            lastSeen: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          const mockTokens: AuthTokens = {
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
            expiresIn: 3600,
          };

          set({
            user: mockUser,
            tokens: mockTokens,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      loginWithFirebase: async (idToken: string) => {
        set({ isLoading: true });
        try {
          const response = await authApi.authenticateWithFirebase({ idToken });
          const { user, tokens } = response.data;
          
          setAuthToken(tokens.accessToken);
          set({
            user,
            tokens,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: async () => {
        try {
          const { tokens } = get();
          if (tokens) {
            await authApi.logout();
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          removeAuthToken();
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      },

      refreshToken: async () => {
        const { tokens } = get();
        if (!tokens?.refreshToken) {
          throw new Error('No refresh token available');
        }

        try {
          const response = await authApi.refreshToken({ refreshToken: tokens.refreshToken });
          const newTokens = response.data;
          
          setAuthToken(newTokens.accessToken);
          set({ tokens: newTokens });
        } catch (error) {
          // If refresh fails, logout user
          get().logout();
          throw error;
        }
      },

      checkAuth: async () => {
        const { tokens } = get();
        if (!tokens?.accessToken) {
          set({ isLoading: false });
          return;
        }

        set({ isLoading: true });
        try {
          setAuthToken(tokens.accessToken);
          const response = await authApi.getProfile();
          const user = response.data;
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          // Token might be expired, try to refresh
          try {
            await get().refreshToken();
            const response = await authApi.getProfile();
            const user = response.data;
            
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
            });
          } catch (refreshError) {
            // Both access and refresh tokens are invalid
            get().clearAuth();
          }
        }
      },

      setUser: (user: User) => {
        set({ user });
      },

      clearAuth: () => {
        removeAuthToken();
        set({
          user: null,
          tokens: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },
    }),
    {
      name: 'chatpay-auth',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
