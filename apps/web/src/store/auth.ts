import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, AuthTokens } from '@chatpay/shared/types';
import { authApi } from '@/lib/api/auth';
import { setAuthToken, removeAuthToken } from '@/lib/api/client';

interface LoginResponse {
  user: User;
  tokens: AuthTokens;
}

interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>;
  register: (data: {
    email: string;
    password: string;
    username: string;
    displayName: string;
    phoneNumber?: string;
  }) => Promise<void>;
  loginWithFirebase: (idToken: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  checkAuth: () => Promise<void>;
  setUser: (user: User) => void;
  clearAuth: () => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      tokens: null,
      isLoading: false,
      isAuthenticated: false,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true });
        try {
          const { user, tokens } = await authApi.login({ 
            email, 
            password 
          });
          
          set({
            user,
            tokens,
            isAuthenticated: true,
            isLoading: false,
          });
          
          setAuthToken(tokens.accessToken);
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      register: async (data: {
        email: string;
        password: string;
        username: string;
        displayName: string;
        phoneNumber?: string;
      }) => {
        set({ isLoading: true });
        try {
          const { user, tokens } = await authApi.register({
            email: data.email,
            password: data.password,
            username: data.username,
            displayName: data.displayName,
            phoneNumber: data.phoneNumber || '',
          });
          
          set({
            user,
            tokens,
            isAuthenticated: true,
            isLoading: false,
          });
          
          setAuthToken(tokens.accessToken);
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      loginWithFirebase: async (idToken: string) => {
        set({ isLoading: true });
        try {
          const { user, tokens } = await authApi.authenticateWithFirebase({
            idToken,
          });
          
          set({
            user,
            tokens,
            isAuthenticated: true,
            isLoading: false,
          });
          
          setAuthToken(tokens.accessToken);
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: async () => {
        try {
          const { tokens } = get();
          if (tokens) {
            await authApi.logout();
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          removeAuthToken();
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      },

      refreshToken: async () => {
        const { tokens } = get();
        if (!tokens?.refreshToken) {
          throw new Error('No refresh token available');
        }

        try {
          const newTokens = await authApi.refreshToken({ 
            refreshToken: tokens.refreshToken 
          });
          
          set({
            tokens: {
              ...tokens,
              ...newTokens,
            },
          });
          
          setAuthToken(newTokens.accessToken);
        } catch (error) {
          console.error('Failed to refresh token:', error);
          get().clearAuth();
          throw error;
        }
      },

      checkAuth: async () => {
        const { tokens } = get();
        if (!tokens?.accessToken) {
          set({ isLoading: false });
          return;
        }

        set({ isLoading: true });
        try {
          setAuthToken(tokens.accessToken);
          const user = await authApi.getProfile();
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          // Token might be expired, try to refresh
          try {
            await get().refreshToken();
            const user = await authApi.getProfile();
            
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
            });
          } catch (refreshError) {
            // Both access and refresh tokens are invalid
            get().clearAuth();
          }
        }
      },

      setUser: (user: User) => {
        set({ user });
      },

      clearAuth: () => {
        removeAuthToken();
        set({
          user: null,
          tokens: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },
    }),
    {
      name: 'chatpay-auth',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
