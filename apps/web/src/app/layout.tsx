import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from '@/components/providers';
import { Toaster } from 'react-hot-toast';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'ChatPay - Secure Messaging & Payments',
  description: 'Secure WhatsApp alternative with built-in fiat/crypto payment functionality',
  keywords: ['chat', 'messaging', 'payments', 'crypto', 'secure', 'encrypted'],
  authors: [{ name: 'ChatPay Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#8B5CF6',
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  openGraph: {
    title: 'ChatPay - Secure Messaging & Payments',
    description: 'Secure WhatsApp alternative with built-in fiat/crypto payment functionality',
    type: 'website',
    locale: 'en_US',
    siteName: 'ChatPay',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ChatPay - Secure Messaging & Payments',
    description: 'Secure WhatsApp alternative with built-in fiat/crypto payment functionality',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} min-h-screen bg-gradient-dark`}>
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1F2937',
                color: '#F9FAFB',
                border: '1px solid #374151',
              },
              success: {
                iconTheme: {
                  primary: '#10B981',
                  secondary: '#F9FAFB',
                },
              },
              error: {
                iconTheme: {
                  primary: '#EF4444',
                  secondary: '#F9FAFB',
                },
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
