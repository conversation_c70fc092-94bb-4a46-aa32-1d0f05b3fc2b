{"name": "@chatpay/web", "version": "1.0.0", "description": "ChatPay Web Application", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 3002", "start": "next start -p 3002", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "lucide-react": "^0.288.0", "zustand": "^4.4.4", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.5.1", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0"}, "devDependencies": {"@types/node": "^20.8.6", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "autoprefixer": "^10.4.16", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}}