{"extends": "../../tsconfig.json", "compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "paths": {"@/*": ["src/*"], "@chatpay/shared": ["../shared/src/index.ts"], "@chatpay/shared/*": ["../shared/src/*"], "@chatpay/crypto": ["../crypto/src/index.ts"], "@chatpay/crypto/*": ["../crypto/src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}