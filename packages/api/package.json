{"name": "@chatpay/api", "version": "1.0.0", "description": "ChatPay NestJS API Backend", "main": "dist/main.js", "scripts": {"build": "nest build", "dev": "nest start --watch", "start": "node dist/main", "start:debug": "nest start --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/jwt": "^10.1.1", "@nestjs/passport": "^10.0.2", "@nestjs/swagger": "^7.1.11", "@nestjs/throttler": "^5.0.0", "@nestjs/websockets": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@prisma/client": "^5.4.2", "@chatpay/shared": "file:../shared", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "firebase-admin": "^11.11.0", "socket.io": "^4.7.2", "multer": "^1.4.5-lts.1", "helmet": "^7.0.0", "cors": "^2.8.5", "compression": "^1.7.4", "express-rate-limit": "^6.10.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/bcryptjs": "^2.4.4", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/multer": "^1.4.7", "@types/cors": "^2.8.14", "@types/compression": "^1.7.3", "jest": "^29.5.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}