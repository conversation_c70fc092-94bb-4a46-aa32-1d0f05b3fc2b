import { Controller, Get, Put, Post, Delete, Body, Param, Query, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UsersService } from './users.service';
import { UpdateUserDto, SearchUsersDto, AddContactDto, UpdateSignalKeysDto } from './dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(private usersService: UsersService) {}

  @Get('profile')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'User profile retrieved' })
  async getProfile(@Req() req: any) {
    return this.usersService.findById(req.user.id);
  }

  @Put('profile')
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  async updateProfile(@Req() req: any, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.updateProfile(req.user.id, updateUserDto);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search users' })
  @ApiResponse({ status: 200, description: 'Users found' })
  async searchUsers(@Query() searchUsersDto: SearchUsersDto) {
    return this.usersService.searchUsers(searchUsersDto);
  }

  @Get('contacts')
  @ApiOperation({ summary: 'Get user contacts' })
  @ApiResponse({ status: 200, description: 'Contacts retrieved' })
  async getContacts(@Req() req: any) {
    return this.usersService.getUserContacts(req.user.id);
  }

  @Post('contacts')
  @ApiOperation({ summary: 'Add a contact' })
  @ApiResponse({ status: 201, description: 'Contact added successfully' })
  async addContact(@Req() req: any, @Body() addContactDto: AddContactDto) {
    await this.usersService.addContact(req.user.id, addContactDto.contactId, addContactDto.nickname);
    return { message: 'Contact added successfully' };
  }

  @Delete('contacts/:contactId')
  @ApiOperation({ summary: 'Remove a contact' })
  @ApiResponse({ status: 200, description: 'Contact removed successfully' })
  async removeContact(@Req() req: any, @Param('contactId') contactId: string) {
    await this.usersService.removeContact(req.user.id, contactId);
    return { message: 'Contact removed successfully' };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User found' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(@Param('id') id: string) {
    return this.usersService.findById(id);
  }

  @Put('signal-keys')
  @ApiOperation({ summary: 'Update Signal protocol keys' })
  @ApiResponse({ status: 200, description: 'Signal keys updated' })
  async updateSignalKeys(@Req() req: any, @Body() updateSignalKeysDto: UpdateSignalKeysDto) {
    await this.usersService.updateSignalKeys(
      req.user.id,
      updateSignalKeysDto.identityKey,
      updateSignalKeysDto.registrationId,
      updateSignalKeysDto.preKeys,
      updateSignalKeysDto.signedPreKey,
    );
    return { message: 'Signal keys updated successfully' };
  }

  @Get(':id/signal-keys')
  @ApiOperation({ summary: 'Get Signal protocol keys for a user' })
  @ApiResponse({ status: 200, description: 'Signal keys retrieved' })
  async getSignalKeys(@Param('id') id: string) {
    return this.usersService.getSignalKeys(id);
  }
}
