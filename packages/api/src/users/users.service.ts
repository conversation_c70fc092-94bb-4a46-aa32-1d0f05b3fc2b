import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { UpdateUserDto, SearchUsersDto } from './dto';
import { User, PaginatedResponse } from '@chatpay/shared';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findById(id: string): Promise<User> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      include: {
        bankAccounts: true,
        cryptoWallets: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { username },
    });
  }

  async updateProfile(userId: string, updateUserDto: UpdateUserDto): Promise<User> {
    const { username, displayName, avatar, phoneNumber } = updateUserDto;

    // Check if username is already taken by another user
    if (username) {
      const existingUser = await this.prisma.user.findFirst({
        where: {
          username,
          NOT: { id: userId },
        },
      });

      if (existingUser) {
        throw new ConflictException('Username is already taken');
      }
    }

    const user = await this.prisma.user.update({
      where: { id: userId },
      data: {
        ...(username && { username }),
        ...(displayName && { displayName }),
        ...(avatar && { avatar }),
        ...(phoneNumber && { phoneNumber }),
      },
    });

    return user;
  }

  async updateOnlineStatus(userId: string, isOnline: boolean): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        isOnline,
        lastSeen: new Date(),
      },
    });
  }

  async searchUsers(searchUsersDto: SearchUsersDto): Promise<PaginatedResponse<User>> {
    const { query, page = 1, limit = 20 } = searchUsersDto;
    const skip = (page - 1) * limit;

    const where = {
      OR: [
        { username: { contains: query, mode: 'insensitive' as const } },
        { displayName: { contains: query, mode: 'insensitive' as const } },
        { email: { contains: query, mode: 'insensitive' as const } },
      ],
    };

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        skip,
        take: limit,
        select: {
          id: true,
          username: true,
          displayName: true,
          avatar: true,
          isOnline: true,
          lastSeen: true,
          isVerified: true,
        },
      }),
      this.prisma.user.count({ where }),
    ]);

    return {
      success: true,
      data: users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getUserContacts(userId: string): Promise<User[]> {
    const contacts = await this.prisma.contact.findMany({
      where: { userId },
      include: {
        contact: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
            isOnline: true,
            lastSeen: true,
            isVerified: true,
          },
        },
      },
    });

    return contacts.map(contact => contact.contact);
  }

  async addContact(userId: string, contactId: string, nickname?: string): Promise<void> {
    // Check if contact exists
    const contactUser = await this.prisma.user.findUnique({
      where: { id: contactId },
    });

    if (!contactUser) {
      throw new NotFoundException('User not found');
    }

    // Check if already a contact
    const existingContact = await this.prisma.contact.findUnique({
      where: {
        userId_contactId: {
          userId,
          contactId,
        },
      },
    });

    if (existingContact) {
      throw new ConflictException('User is already in contacts');
    }

    await this.prisma.contact.create({
      data: {
        userId,
        contactId,
        nickname,
      },
    });
  }

  async removeContact(userId: string, contactId: string): Promise<void> {
    const contact = await this.prisma.contact.findUnique({
      where: {
        userId_contactId: {
          userId,
          contactId,
        },
      },
    });

    if (!contact) {
      throw new NotFoundException('Contact not found');
    }

    await this.prisma.contact.delete({
      where: {
        userId_contactId: {
          userId,
          contactId,
        },
      },
    });
  }

  async updateSignalKeys(
    userId: string,
    identityKey: string,
    registrationId: number,
    preKeys: any,
    signedPreKey: any,
  ): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        identityKey,
        registrationId,
        preKeys,
        signedPreKey,
      },
    });
  }

  async getSignalKeys(userId: string): Promise<{
    identityKey: string;
    registrationId: number;
    preKeys: any;
    signedPreKey: any;
  } | null> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        identityKey: true,
        registrationId: true,
        preKeys: true,
        signedPreKey: true,
      },
    });

    if (!user || !user.identityKey) {
      return null;
    }

    return {
      identityKey: user.identityKey,
      registrationId: user.registrationId!,
      preKeys: user.preKeys,
      signedPreKey: user.signedPreKey,
    };
  }
}
