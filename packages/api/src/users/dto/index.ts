import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eng<PERSON>, IsPhoneNumber, IsNumber, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class UpdateUserDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(20)
  username?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  displayName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsPhoneNumber()
  phoneNumber?: string;
}

export class SearchUsersDto {
  @ApiProperty({ description: 'Search query' })
  @IsString()
  @MinLength(1)
  query: string;

  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number;

  @ApiProperty({ required: false, default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number;
}

export class AddContactDto {
  @ApiProperty({ description: 'User ID to add as contact' })
  @IsString()
  contactId: string;

  @ApiProperty({ required: false, description: 'Nickname for the contact' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  nickname?: string;
}

export class UpdateSignalKeysDto {
  @ApiProperty({ description: 'Signal identity key' })
  @IsString()
  identityKey: string;

  @ApiProperty({ description: 'Signal registration ID' })
  @IsNumber()
  registrationId: number;

  @ApiProperty({ description: 'Signal pre-keys' })
  @IsObject()
  preKeys: any;

  @ApiProperty({ description: 'Signal signed pre-key' })
  @IsObject()
  signedPreKey: any;
}
