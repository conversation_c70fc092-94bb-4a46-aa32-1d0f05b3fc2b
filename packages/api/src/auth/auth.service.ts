import { Injectable, UnauthorizedException, ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import * as admin from 'firebase-admin';
import { PrismaService } from '../prisma/prisma.service';
import { UsersService } from '../users/users.service';
import { LoginDto, RegisterDto, FirebaseAuthDto } from './dto';
import { AuthTokens, User } from '@chatpay/shared';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private usersService: UsersService,
  ) {
    this.initializeFirebase();
  }

  private initializeFirebase() {
    if (!admin.apps.length) {
      const serviceAccount = {
        projectId: this.configService.get('FIREBASE_PROJECT_ID'),
        clientEmail: this.configService.get('FIREBASE_CLIENT_EMAIL'),
        privateKey: this.configService.get('FIREBASE_PRIVATE_KEY')?.replace(/\\n/g, '\n'),
      };

      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
    }
  }

  async register(registerDto: RegisterDto): Promise<{ user: User; tokens: AuthTokens }> {
    const { email, password, username, displayName, phoneNumber } = registerDto;

    // Check if user already exists
    const existingUser = await this.prisma.user.findFirst({
      where: {
        OR: [
          { email },
          { username },
        ],
      },
    });

    if (existingUser) {
      throw new ConflictException('User with this email or username already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create Firebase user
    let firebaseUser;
    try {
      firebaseUser = await admin.auth().createUser({
        email,
        password,
        displayName,
        phoneNumber,
      });
    } catch (error) {
      throw new ConflictException('Failed to create Firebase user');
    }

    // Create user in database
    const user = await this.prisma.user.create({
      data: {
        email,
        username,
        displayName,
        phoneNumber,
        firebaseUid: firebaseUser.uid,
        isVerified: false,
      },
    });

    // Generate tokens
    const tokens = await this.generateTokens(user.id, user.email);

    return {
      user: this.excludePassword(user),
      tokens,
    };
  }

  async login(loginDto: LoginDto): Promise<{ user: User; tokens: AuthTokens }> {
    const { email, password } = loginDto;

    // Find user
    const user = await this.prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Verify Firebase user
    try {
      const firebaseUser = await admin.auth().getUserByEmail(email);
      
      // Verify password with Firebase
      // Note: In production, you might want to use Firebase Auth directly
      // For now, we'll use a simple password check
      const isPasswordValid = await this.verifyFirebasePassword(email, password);
      
      if (!isPasswordValid) {
        throw new UnauthorizedException('Invalid credentials');
      }
    } catch (error) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Update last seen
    await this.prisma.user.update({
      where: { id: user.id },
      data: { lastSeen: new Date() },
    });

    // Generate tokens
    const tokens = await this.generateTokens(user.id, user.email);

    return {
      user: this.excludePassword(user),
      tokens,
    };
  }

  async authenticateWithFirebase(firebaseAuthDto: FirebaseAuthDto): Promise<{ user: User; tokens: AuthTokens }> {
    const { idToken } = firebaseAuthDto;

    try {
      // Verify Firebase ID token
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      const { uid, email, name, phone_number } = decodedToken;

      // Find or create user
      let user = await this.prisma.user.findUnique({
        where: { firebaseUid: uid },
      });

      if (!user) {
        // Create new user
        const username = this.generateUsername(email);
        user = await this.prisma.user.create({
          data: {
            email,
            username,
            displayName: name || email.split('@')[0],
            phoneNumber: phone_number,
            firebaseUid: uid,
            isVerified: true,
          },
        });
      } else {
        // Update last seen
        await this.prisma.user.update({
          where: { id: user.id },
          data: { lastSeen: new Date() },
        });
      }

      // Generate tokens
      const tokens = await this.generateTokens(user.id, user.email);

      return {
        user: this.excludePassword(user),
        tokens,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid Firebase token');
    }
  }

  async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      const payload = this.jwtService.verify(refreshToken);
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      return this.generateTokens(user.id, user.email);
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(userId: string): Promise<void> {
    // Update user status
    await this.prisma.user.update({
      where: { id: userId },
      data: { 
        isOnline: false,
        lastSeen: new Date(),
      },
    });
  }

  async validateUser(userId: string): Promise<User | null> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    return user ? this.excludePassword(user) : null;
  }

  private async generateTokens(userId: string, email: string): Promise<AuthTokens> {
    const payload = { email, sub: userId };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, { expiresIn: '15m' }),
      this.jwtService.signAsync(payload, { expiresIn: '7d' }),
    ]);

    return {
      accessToken,
      refreshToken,
      expiresIn: 15 * 60, // 15 minutes
    };
  }

  private async verifyFirebasePassword(email: string, password: string): Promise<boolean> {
    // In a real implementation, you would use Firebase Auth REST API
    // For now, we'll return true for demo purposes
    // TODO: Implement proper Firebase password verification
    return true;
  }

  private generateUsername(email: string): string {
    const baseUsername = email.split('@')[0];
    const randomSuffix = Math.floor(Math.random() * 1000);
    return `${baseUsername}${randomSuffix}`;
  }

  private excludePassword(user: any): User {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
}
