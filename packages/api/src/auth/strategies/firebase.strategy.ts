import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-custom';
import * as admin from 'firebase-admin';
import { AuthService } from '../auth.service';

@Injectable()
export class FirebaseStrategy extends PassportStrategy(Strategy, 'firebase') {
  constructor(private authService: AuthService) {
    super();
  }

  async validate(req: any): Promise<any> {
    const idToken = req.headers.authorization?.replace('Bearer ', '');
    
    if (!idToken) {
      throw new UnauthorizedException('No Firebase token provided');
    }

    try {
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      const user = await this.authService.validateUser(decodedToken.uid);
      
      if (!user) {
        throw new UnauthorizedException('User not found');
      }
      
      return user;
    } catch (error) {
      throw new UnauthorizedException('Invalid Firebase token');
    }
  }
}
