{"name": "@chatpay/shared", "version": "1.0.0", "description": "Shared types, utilities, and components for ChatPay", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"zod": "^3.22.4", "date-fns": "^2.30.0", "crypto-js": "^4.1.1"}, "devDependencies": {"@types/crypto-js": "^4.1.2", "typescript": "^5.2.2"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "default": "./dist/types/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.js"}}}