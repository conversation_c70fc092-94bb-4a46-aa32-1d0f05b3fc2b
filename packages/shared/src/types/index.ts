import { z } from 'zod';

// User Types
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  username: z.string(),
  displayName: z.string(),
  avatar: z.string().nullable(),
  phoneNumber: z.string().nullable(),
  isVerified: z.boolean(),
  isOnline: z.boolean(),
  lastSeen: z.date(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type User = z.infer<typeof UserSchema>;

// Chat Types
export const ChatSchema = z.object({
  id: z.string(),
  name: z.string().nullable(),
  description: z.string().nullable(),
  avatar: z.string().nullable(),
  isGroup: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Chat = z.infer<typeof ChatSchema>;

// Message Types
export enum MessageType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  FILE = 'FILE',
  AUDIO = 'AUDIO',
  VIDEO = 'VIDEO',
  PAYMENT = 'PAYMENT',
  CRYPTO_PAYMENT = 'CRYPTO_PAYMENT',
}

export enum DeliveryStatus {
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
}

export const MessageSchema = z.object({
  id: z.string(),
  content: z.string().nullable(),
  messageType: z.nativeEnum(MessageType),
  fileUrl: z.string().nullable(),
  fileName: z.string().nullable(),
  fileSize: z.number().nullable(),
  isEdited: z.boolean(),
  isDeleted: z.boolean(),
  senderId: z.string(),
  chatId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Message = z.infer<typeof MessageSchema>;

// Payment Types
export enum PaymentStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export enum PaymentMethod {
  ACH = 'ACH',
  WIRE = 'WIRE',
  CARD = 'CARD',
  DWOLLA = 'DWOLLA',
}

export const PaymentSchema = z.object({
  id: z.string(),
  amount: z.number(),
  currency: z.string(),
  description: z.string().nullable(),
  status: z.nativeEnum(PaymentStatus),
  paymentMethod: z.nativeEnum(PaymentMethod),
  senderId: z.string(),
  receiverId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Payment = z.infer<typeof PaymentSchema>;

// Crypto Types
export enum CryptoTxStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export const CryptoTransactionSchema = z.object({
  id: z.string(),
  amount: z.string(),
  currency: z.string(),
  network: z.string(),
  txHash: z.string().nullable(),
  status: z.nativeEnum(CryptoTxStatus),
  senderId: z.string(),
  receiverId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type CryptoTransaction = z.infer<typeof CryptoTransactionSchema>;

// Socket Events
export interface SocketEvents {
  // Connection
  connect: () => void;
  disconnect: () => void;
  
  // Authentication
  authenticate: (token: string) => void;
  authenticated: (user: User) => void;
  
  // Messages
  'message:send': (data: {
    chatId: string;
    content: string;
    messageType: MessageType;
    fileUrl?: string;
  }) => void;
  'message:receive': (message: Message) => void;
  'message:status': (data: {
    messageId: string;
    status: DeliveryStatus;
  }) => void;
  
  // Chat
  'chat:join': (chatId: string) => void;
  'chat:leave': (chatId: string) => void;
  'chat:typing': (data: { chatId: string; isTyping: boolean }) => void;
  
  // User Status
  'user:online': (userId: string) => void;
  'user:offline': (userId: string) => void;
  
  // Payments
  'payment:send': (payment: Payment) => void;
  'payment:receive': (payment: Payment) => void;
  'payment:status': (data: {
    paymentId: string;
    status: PaymentStatus;
  }) => void;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Auth Types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  username: string;
  displayName: string;
  phoneNumber?: string;
}

// Theme Types (Purple/Gold/Black/Gray)
export interface ThemeColors {
  primary: {
    50: string;
    100: string;
    500: string;
    600: string;
    700: string;
    900: string;
  };
  accent: {
    50: string;
    100: string;
    500: string;
    600: string;
    700: string;
    900: string;
  };
  neutral: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
}

export const THEME_COLORS: ThemeColors = {
  primary: {
    50: '#F3E8FF',
    100: '#E9D5FF',
    500: '#8B5CF6',
    600: '#7C3AED',
    700: '#6D28D9',
    900: '#4C1D95',
  },
  accent: {
    50: '#FFFBEB',
    100: '#FEF3C7',
    500: '#F59E0B',
    600: '#D97706',
    700: '#B45309',
    900: '#78350F',
  },
  neutral: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },
};

// File Upload Types
export interface FileUpload {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  url?: string;
  error?: string;
}

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'application/pdf',
  'text/plain',
  'audio/mpeg',
  'audio/wav',
  'video/mp4',
  'video/webm',
];

// Error Types
export class ChatPayError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'ChatPayError';
  }
}

export class ValidationError extends ChatPayError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR', 400);
  }
}

export class AuthenticationError extends ChatPayError {
  constructor(message: string = 'Authentication required') {
    super(message, 'AUTHENTICATION_ERROR', 401);
  }
}

export class AuthorizationError extends ChatPayError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 'AUTHORIZATION_ERROR', 403);
  }
}

export class NotFoundError extends ChatPayError {
  constructor(message: string = 'Resource not found') {
    super(message, 'NOT_FOUND_ERROR', 404);
  }
}

export class PaymentError extends ChatPayError {
  constructor(message: string) {
    super(message, 'PAYMENT_ERROR', 400);
  }
}
