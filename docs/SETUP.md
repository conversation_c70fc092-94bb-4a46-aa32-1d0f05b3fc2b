# ChatPay Setup Guide

This guide will walk you through setting up ChatPay for development and production.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js 18+**: [Download from nodejs.org](https://nodejs.org/)
- **PostgreSQL**: [Download from postgresql.org](https://www.postgresql.org/)
- **Git**: [Download from git-scm.com](https://git-scm.com/)

## Development Setup

### 1. <PERSON>lone and Install

```bash
git clone https://github.com/yourusername/chatpay.git
cd chatpay
chmod +x install.sh
./install.sh
```

### 2. Database Setup

Create a PostgreSQL database:

```sql
CREATE DATABASE chatpay;
CREATE USER chatpay_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE chatpay TO chatpay_user;
```

### 3. Environment Configuration

Copy the example environment file and update it:

```bash
cp .env.example .env
```

Update the following variables in `.env`:

```env
# Database
DATABASE_URL="postgresql://chatpay_user:your_password@localhost:5432/chatpay"

# Firebase Configuration
FIREBASE_API_KEY="your-firebase-api-key"
FIREBASE_AUTH_DOMAIN="your-project.firebaseapp.com"
FIREBASE_PROJECT_ID="your-project-id"
FIREBASE_STORAGE_BUCKET="your-project.appspot.com"
FIREBASE_MESSAGING_SENDER_ID="*********"
FIREBASE_APP_ID="1:*********:web:abcdef"

# JWT Secret (generate a secure random string)
JWT_SECRET="your-super-secret-jwt-key-here"

# Plaid Configuration (for bank integration)
PLAID_CLIENT_ID="your-plaid-client-id"
PLAID_SECRET="your-plaid-secret"
PLAID_ENV="sandbox"

# Dwolla Configuration (for payments)
DWOLLA_KEY="your-dwolla-key"
DWOLLA_SECRET="your-dwolla-secret"
DWOLLA_ENVIRONMENT="sandbox"

# Stripe Configuration (alternative payment processor)
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_PUBLISHABLE_KEY="pk_test_..."

# Socket.IO Configuration
SOCKET_IO_PORT=3001

# API Configuration
API_PORT=3000
API_URL="http://localhost:3000"
WEB_URL="http://localhost:3002"
```

### 4. Firebase Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project
3. Enable Authentication with Email/Password and Google providers
4. Get your configuration from Project Settings > General > Your apps
5. Update the Firebase variables in your `.env` file

### 5. Plaid Setup (for Bank Integration)

1. Sign up at [Plaid Dashboard](https://dashboard.plaid.com/)
2. Get your Client ID and Secret from the Keys section
3. Update the Plaid variables in your `.env` file

### 6. Database Migration

Run the database migrations:

```bash
npm run db:migrate
```

Optionally, seed the database with sample data:

```bash
npm run db:seed
```

### 7. Start Development Servers

Start all development servers:

```bash
npm run dev
```

This will start:
- **Web App**: http://localhost:3002
- **API Server**: http://localhost:3000
- **Socket.IO Server**: http://localhost:3001
- **API Documentation**: http://localhost:3000/api/docs

## Production Deployment

### Web Application (Vercel)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### API Server (Railway/Render)

1. Create a new service on Railway or Render
2. Connect your GitHub repository
3. Set environment variables
4. Deploy the `packages/api` directory

### Database (Supabase)

1. Create a new project on [Supabase](https://supabase.com/)
2. Get your database URL from Settings > Database
3. Update `DATABASE_URL` in your production environment

### Mobile App (Expo EAS)

```bash
cd apps/mobile
npm install -g @expo/cli
npm install -g eas-cli
eas login
eas build:configure
eas build --platform all
```

## Troubleshooting

### Common Issues

**Database Connection Error**
- Ensure PostgreSQL is running
- Check your DATABASE_URL format
- Verify database credentials

**Firebase Authentication Error**
- Check Firebase configuration
- Ensure Authentication is enabled in Firebase Console
- Verify API keys are correct

**Socket.IO Connection Issues**
- Check if port 3001 is available
- Verify CORS settings in the API
- Check firewall settings

**Build Errors**
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Clear Turbo cache: `npx turbo clean`
- Check TypeScript errors: `npm run type-check`

### Getting Help

- Check the [GitHub Issues](https://github.com/yourusername/chatpay/issues)
- Join our [Discord Community](https://discord.gg/chatpay)
- Email support: <EMAIL>

## Security Considerations

### Development
- Never commit `.env` files
- Use strong JWT secrets
- Keep API keys secure

### Production
- Use HTTPS everywhere
- Enable rate limiting
- Monitor for suspicious activity
- Regular security updates

## Performance Optimization

### Database
- Add proper indexes
- Use connection pooling
- Monitor query performance

### Frontend
- Enable compression
- Use CDN for static assets
- Implement proper caching

### API
- Use Redis for caching
- Implement request throttling
- Monitor API performance

## Monitoring and Logging

### Sentry (Error Tracking)
1. Create account at [Sentry.io](https://sentry.io/)
2. Add DSN to environment variables
3. Errors will be automatically tracked

### Logtail (Logging)
1. Create account at [Logtail](https://logtail.com/)
2. Add token to environment variables
3. Logs will be centralized

## Backup and Recovery

### Database Backups
```bash
# Create backup
pg_dump chatpay > backup.sql

# Restore backup
psql chatpay < backup.sql
```

### File Backups
- Use cloud storage for uploaded files
- Regular automated backups
- Test restore procedures

## Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for development guidelines and contribution process.

## License

This project is licensed under the MIT License - see [LICENSE](LICENSE) for details.
