#!/bin/bash

# ChatPay Installation Script
echo "🚀 Installing ChatPay - Secure Messaging & Payments Platform"
echo "============================================================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm $(npm -v) detected"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
npm install

# Install dependencies for all packages
echo ""
echo "📦 Installing shared package dependencies..."
cd packages/shared && npm install && cd ../..

echo ""
echo "📦 Installing API package dependencies..."
cd packages/api && npm install && cd ../..

echo ""
echo "📦 Installing web app dependencies..."
cd apps/web && npm install && cd ../..

# Generate Prisma client
echo ""
echo "🗄️  Generating Prisma client..."
npx prisma generate

# Copy environment file
if [ ! -f .env ]; then
    echo ""
    echo "📝 Creating environment file..."
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
    echo "⚠️  Please update the .env file with your actual configuration values"
else
    echo "✅ .env file already exists"
fi

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "Next steps:"
echo "1. Update your .env file with your database and API keys"
echo "2. Set up your PostgreSQL database"
echo "3. Run 'npm run db:migrate' to create database tables"
echo "4. Run 'npm run dev' to start the development servers"
echo ""
echo "📚 Documentation: Check the README.md file for detailed setup instructions"
echo "🐛 Issues: Report any issues on GitHub"
echo ""
echo "Happy coding! 🚀"
